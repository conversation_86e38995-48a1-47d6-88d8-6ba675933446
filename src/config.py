"""
Configuration management for the power plant data retrieval pipeline.
"""
import os
from typing import Dict, List
from dotenv import load_dotenv
from src.models import PipelineConfig

# Load environment variables
load_dotenv()


class Config:
    """Central configuration class."""

    def __init__(self):
        self.pipeline = PipelineConfig(
            max_search_results=int(os.getenv('MAX_SEARCH_RESULTS', 10)),
            max_scrape_pages=int(os.getenv('MAX_SCRAPE_PAGES', 5)),
            request_timeout=int(os.getenv('REQUEST_TIMEOUT', 60)),
            retry_attempts=int(os.getenv('RETRY_ATTEMPTS', 3)),
            min_content_length=int(os.getenv('MIN_CONTENT_LENGTH', 100)),
            max_content_length=int(os.getenv('MAX_CONTENT_LENGTH', 50000)),
            confidence_threshold=float(os.getenv('CONFIDENCE_THRESHOLD', 0.7)),
            # Use SCRAPER_API_KEY for both search and scraping since you're using ScraperAPI for both
            serp_api_key=os.getenv('SCRAPER_API_KEY', ''),
            scraper_api_key=os.getenv('SCRAPER_API_KEY', ''),
            groq_api_key=os.getenv('GROQ_API_KEY', ''),
            # OpenAI settings
            openai_api_key=os.getenv('OPENAI_API_KEY', ''),
            openai_model=os.getenv('OPENAI_MODEL', 'gpt-4o-mini')
        )

    @property
    def search_query_templates(self) -> Dict[str, List[str]]:
        """Search query templates for different extraction phases."""
        return {
            "basic_discovery": [
                "{plant_name} power plant",
                "{plant_name} power station",
                "{plant_name} generating facility",
                "{plant_name} energy plant"
            ],
            "organizational": [
                "{plant_name} owner operator company",
                "{plant_name} power company",
                "{plant_name} utility company",
                "{plant_name} private public ownership",
                "{plant_name} government state owned",
                "{organization_name} power plants"  # Used when org found
            ],
            "technical_details": [
                "{plant_name} capacity MW specifications",
                "{plant_name} power plant type technology",
                "{plant_name} coal gas nuclear solar wind",
                "{plant_name} generation capacity"
            ],
            "location_details": [
                "{plant_name} location country province",
                "{plant_name} address location",
                "{plant_name} {country} {province}"  # Used when partially known
            ],
            "ppa_details": [
                "{plant_name} PPA power purchase agreement",
                "{plant_name} offtake agreement",
                "{plant_name} long term contract",
                "{plant_name} electricity sales agreement"
            ],
            "portfolio_details": [
                "{organization_name} power plants portfolio",
                "{organization_name} generating assets",
                "{organization_name} energy facilities",
                "{organization_name} power generation capacity"
            ],
            # New search categories for plant technical details
            "grid_connectivity": [
                "{plant_name} grid connection substation",
                "{plant_name} transmission line connection",
                "{plant_name} electrical grid tie-in",
                "{plant_name} substation interconnection",
                "{plant_name} grid infrastructure",
                "{plant_name} transmission system connection"
            ],
            "plant_coordinates": [
                "{plant_name} latitude longitude coordinates",
                "{plant_name} GPS location coordinates",
                "{plant_name} exact location address",
                "{plant_name} geographic coordinates",
                "{plant_name} site location map",
                "{plant_name} plant address location",
                "{plant_name} facility address",
                "{plant_name} power plant location details"
            ],
            "ppa_contracts": [
                "{plant_name} power purchase agreement details",
                "{plant_name} PPA contract terms",
                "{plant_name} offtake agreement pricing",
                "{plant_name} electricity sales contract",
                "{plant_name} long term power contract",
                "{plant_name} utility purchase agreement"
            ],
            "plant_units": [
                "{plant_name} generation units turbines",
                "{plant_name} individual units capacity",
                "{plant_name} unit specifications",
                "{plant_name} turbine generator details",
                "{plant_name} plant units configuration",
                "{plant_name} generation equipment"
            ],
            "plant_specifications": [
                "{plant_name} technical specifications",
                "{plant_name} capacity MW rating",
                "{plant_name} plant design details",
                "{plant_name} engineering specifications",
                "{plant_name} facility technical data"
            ]
        }

    @property
    def source_type_indicators(self) -> Dict[str, List[str]]:
        """URL patterns to identify source types."""
        return {
            "company_official": [
                "investor", "about", "corporate", "company",
                "annual-report", "sustainability", "operations",
                "getmedia", "media", "documents", "reports"
            ],
            "regulatory_filing": [
                "sec.gov", "edgar", "10-k", "10-q", "8-k",
                "energy-commission", "utility-commission",
                "filing", "regulatory"
            ],
            "government_database": [
                ".gov", "eia.gov", "energy.gov", "epa.gov",
                "iea.org", "irena.org"
            ],
            "industry_report": [
                "platts", "woodmac", "globaldata", "bnef",
                "power-eng", "utility-dive", "energy-central"
            ],
            "news_article": [
                "reuters", "bloomberg", "wsj", "ft.com",
                "power-technology", "renewableenergyworld"
            ],
            "wikipedia": [
                "wikipedia.org", "wikimedia"
            ]
        }

    @property
    def url_priority_weights(self) -> Dict[str, int]:
        """Priority weights for different source types."""
        return {
            "company_official": 10,
            "regulatory_filing": 9,
            "government_database": 8,
            "industry_report": 7,
            "news_article": 6,
            "wikipedia": 5,
            "other": 3
        }

    @property
    def content_relevance_keywords(self) -> Dict[str, List[str]]:
        """Keywords for content relevance scoring."""
        return {
            "high_relevance": [
                "power plant", "generating station", "power station",
                "MW", "megawatt", "capacity", "generation",
                "electricity", "energy", "utility", "grid",
                "thermal power", "power generation", "electric power"
            ],
            "medium_relevance": [
                "coal", "gas", "nuclear", "solar", "wind", "hydro",
                "biomass", "geothermal", "renewable", "fossil",
                "steam", "turbine", "generator", "boiler"
            ],
            "organizational": [
                "owner", "operator", "subsidiary", "parent company",
                "acquired", "merger", "joint venture", "partnership",
                "board of directors", "management", "executive"
            ],
            "financial": [
                "annual report", "financial year", "fiscal year",
                "revenue", "earnings", "investment", "funding",
                "financial statements", "balance sheet", "income statement"
            ],
            "location": [
                "located", "situated", "based", "province", "state",
                "region", "country", "city", "municipality",
                "address", "coordinates", "district"
            ]
        }

    @property
    def extraction_prompts(self) -> Dict[str, str]:
        """LLM prompts for field extraction."""
        return {
            "cfpp_type": """
From the following content about {plant_name}, determine the ownership type of this power plant.
Look for information about whether this is a private company, public/government entity, or other ownership structure.
Look for phrases like: "private company", "publicly owned", "government owned", "state-owned", "municipal", "cooperative", "joint venture", "public-private partnership".

Content: {content}

Return the ownership type:
- "private" for privately owned companies
- "public" for government/state/municipal owned entities
- "cooperative" for cooperative ownership
- "joint_venture" for joint ventures or partnerships
- "unknown" if ownership type is unclear or not found

Return only one of these values: "private", "public", "cooperative", "joint_venture", "unknown".
""",

            "organization_name": """
From the following content about {plant_name}, identify the official company name that owns or operates this power plant.
Look for phrases like: "owned by" or "operated by", company names in titles, or "power plant" in titles.

Content: {content}

IMPORTANT: Return ONLY the company name, nothing else. No explanations, no additional text.


If unclear or not found, return "unknown".
""",

            "country_name": """
From the following content about {plant_name}, identify the country where this power plant is located.
Look for explicit country mentions, addresses, or geographic references.

Content: {content}

IMPORTANT: Return ONLY the country name, nothing else. No explanations, no additional text.


If unclear or not found, return "unknown".
""",

            "province": """
From the following content about {plant_name}, identify the state, province, or sub-national region where this power plant is located.
Look for state names, province names, regional identifiers in addresses or location descriptions.

Content: {content}

IMPORTANT: Return ONLY the state/province/region name, nothing else. No explanations, no additional text.

If unclear or not found, return "unknown".
""",

            "plants_count": """
From the following content about {organization_name}, determine the number of power plants owned by this organization.
Look for phrases like: "operates X plants", "portfolio of X facilities", "X generating stations", "X power plants".
Count only power plants, not individual generating units.

Content: {content}

Return only a number (integer).
If unclear or not found, return "unknown".
""",

            "plant_types": """
From the following content about {organization_name}, identify all types of power generation technologies operated by this organization.
Look for mentions of: coal, gas, nuclear, solar, wind, hydro, biomass, geothermal, oil, etc.
Return as a list of technologies.

Content: {content}

Return as a comma-separated list .
If unclear or not found, return "unknown".
""",

            "ppa_flag": """
From the following content about {plant_name}, determine the level at which the Power Purchase Agreement (PPA) applies.
Look for: "PPA", "power purchase agreement", "offtake agreement", "long-term contract", "electricity sales agreement".

Determine if the PPA applies at:
- "Plant" level (site-wide agreement covering the entire power plant facility)
- "Unit" level (individual agreements for specific generating units within the plant)

Content: {content}

Return "Plant" if PPA applies to the entire plant/site, "Unit" if PPA applies to individual generating units, "unknown" if unclear or no PPA information found.
""",

            "currency_in": """
From the following content about {organization_name}, identify the primary currency used in financial reporting.
Look for currency symbols, currency codes (USD, EUR, etc.), or explicit mentions of reporting currency.
This is typically the currency of the country where the company is headquartered.

Content: {content}

Return only the 3-letter ISO currency code (e.g., "USD", "EUR", "GBP").
If unclear or not found, return "unknown".
""",

            "financial_year": """
From the following content about {organization_name}, identify the fiscal year period used by this organization.
Look for phrases like: "fiscal year ending March", "financial year April to March", "calendar year", "year ended December".

Based on the country and organization, determine the fiscal year period in MM-MM format:
- If fiscal year ends in March: return "04-03" (April to March)
- If fiscal year ends in December: return "01-12" (January to December)
- If fiscal year ends in June: return "07-06" (July to June)
- If fiscal year ends in September: return "10-09" (October to September)

Common patterns by country:
- India, UK, Japan: typically 04-03 (April to March)
- USA, Germany, most EU: typically 01-12 (January to December)
- Australia: typically 07-06 (July to June)

Content: {content}

Return only the MM-MM format (e.g., "04-03", "01-12", "07-06").
If unclear or not found, return "unknown".
"""
        }

    @property
    def plant_details_extraction_prompts(self) -> Dict[str, str]:
        """LLM prompts for plant technical details extraction."""
        return {
            "name": """
From the following content about {plant_name}, extract the official name of the power plant.
Look for the complete, official name as it appears in documents, not abbreviations.

Content: {content}

IMPORTANT: Return ONLY the plant name, nothing else. No explanations, no additional text.
Examples of correct responses:
- "Jhajjar Power Station"
- "Mahatma Gandhi Super Thermal Power Project"
- "Adani Mundra Power Station"

If not found, return: {plant_name}
""",
            "plant_type": """
From the following content about {plant_name}, determine the technology or fuel type of this power plant.
Look for information about the primary generation technology.

Content: {content}

IMPORTANT: Return ONLY the plant type, nothing else. No explanations, no additional text.
Valid responses: coal, gas, nuclear, solar, wind, hydro, biomass, geothermal, oil, combined_cycle, cogeneration



If not found, return "".
""",
            "plant_address": """
From the following content about {plant_name}, extract the district or city, state, and country location of the power plant.
Look for location information in the format: District or city, State, Country.

Content: {content}

IMPORTANT: Return ONLY the address in the format "District or city, State, Country", nothing else. No explanations, no additional text.


If not found, return "".
""",
            "lat": """
From the following content about {plant_name}, extract the plant's own latitude coordinate in decimal degrees.
Look for GPS coordinates, geographic location data, or map coordinates specifically for the power plant facility.

Content: {content}

IMPORTANT: Return ONLY the latitude number in decimal degrees, nothing else. No explanations, no additional text.


Valid range: -90 to +90. If not found, return "".
""",
            "long": """
From the following content about {plant_name}, extract the plant's own longitude coordinate in decimal degrees.
Look for GPS coordinates, geographic location data, or map coordinates specifically for the power plant facility.

Content: {content}

IMPORTANT: Return ONLY the longitude number in decimal degrees, nothing else. No explanations, no additional text.


Valid range: -180 to +180. If not found, return "".
""",

            "units_id": """
From the following content about {plant_name}, determine the number of units at this plant and list out the integers from 1 to the number of units.
Look for information about generation units, turbines, or generators to determine the total count.

Content: {content}

IMPORTANT: Return ONLY a JSON array of integers, nothing else. No explanations, no additional text.
Examples of correct responses:
- [1, 2] (for a 2-unit plant)
- [1, 2, 3, 4] (for a 4-unit plant)
- [1] (for a single-unit plant)

If not found or unclear, return: []
""",
            "grid_connectivity_maps": """
From the following content about {plant_name}, extract detailed grid connection and substation information.
Look for substation names, transmission line voltages (kV), grid tie-in points, electrical infrastructure details.

Content: {content}

IMPORTANT: Return ONLY valid JSON, nothing else. No explanations, no additional text.

Return a JSON array with this exact structure:
[{
  "details": [{
    "substation_name": "The official name of the substation",
    "substation_type": "The classification and voltage level of the substation, including any regional or directional qualifier",
    "capacity": "The rated capacity of the connection at this substation (e.g., in MW)",
    "latitude": "The geographic latitude of the substation",
    "longitude": "The geographic longitude of the substation",
    "projects": [{
      "distance": "The distance (e.g., in km) from the substation to that project"
    }]
  }]
}]

If no grid information found, return: []
""",

            "ppa_details": """
From the following content about {plant_name}, extract Power Purchase Agreement (PPA) contract details.
Look for contract capacity, duration, start/end dates, counterparty information, pricing details.

Content: {content}

IMPORTANT: Return ONLY valid JSON, nothing else. No explanations, no additional text.

Return a JSON array with this exact structure:
[{
  "capacity": "The capacity covered by this PPA (typically in MW)",
  "capacity_unit": "The unit of that capacity (e.g., 'MW', 'kW')",
  "start_date": "The PPA's commencement date (ISO format, YYYY-MM-DD)",
  "end_date": "The PPA's termination date (ISO format, YYYY-MM-DD)",
  "tenure": "The numeric duration of the PPA (e.g., 20)",
  "tenure_type": "The unit for the tenure (e.g., 'Years', 'Months')",
  "respondents": [{
    "name": "The entity procuring the power from the plant/unit under the terms of the PPA",
    "capacity": "The capacity volume contracted by this respondent",
    "currency": "The currency in which the price is denominated (e.g., 'USD', 'INR')",
    "price": "The contracted price per unit of energy or capacity",
    "price_unit": "The basis for the price (e.g., '$/MWh', 'INR/kW-year')"
  }]
}]

If no PPA information found, return: []
"""
        }

    @property
    def unit_details_extraction_prompts(self) -> Dict[str, str]:
        """LLM prompts for unit technical details extraction."""
        return {
            "capacity": """
From the following content about {plant_name}, extract the capacity of this specific unit in MW.
Look for unit-specific capacity information, not total plant capacity.

Content: {content}

IMPORTANT: Return ONLY the capacity number in MW, nothing else. No explanations, no additional text.
Examples: "660", "800", "1000"

If not found, return "".
""",
            "fuel_type": """
From the following content about {plant_name}, determine the primary fuel type used by this unit.
Look for fuel information like coal, gas, nuclear, etc.

Content: {content}

IMPORTANT: Return ONLY the fuel type, nothing else. No explanations, no additional text.
Valid responses: coal, gas, nuclear, solar, wind, hydro, biomass, geothermal, oil

If not found, return "".
""",
            "technology": """
From the following content about {plant_name}, determine the generation technology of this unit.
Look for technology information like supercritical, subcritical, combined cycle, etc.

Content: {content}

IMPORTANT: Return ONLY the technology type, nothing else. No explanations, no additional text.
Examples: "supercritical", "subcritical", "combined_cycle", "gas_turbine"

If not found, return "".
""",
            "commencement_date": """
From the following content about {plant_name}, extract the commissioning or start date of this unit.
Look for dates when the unit began commercial operation.

Content: {content}

IMPORTANT: Return ONLY the date in YYYY-MM-DD format, nothing else. No explanations, no additional text.
Examples: "2012-01-01", "2015-06-15"

If not found, return "".
""",
            "heat_rate": """
From the following content about {plant_name}, extract the heat rate of this unit.
Look for efficiency metrics in kJ/kWh or BTU/kWh.

Content: {content}

IMPORTANT: Return ONLY the heat rate number, nothing else. No explanations, no additional text.
Examples: "2400", "8500", "9200"

If not found, return "".
""",
            "unit_efficiency": """
From the following content about {plant_name}, extract the thermal efficiency of this unit as a percentage.
Look for efficiency percentages or thermal efficiency data.

Content: {content}

IMPORTANT: Return ONLY the efficiency percentage number, nothing else. No explanations, no additional text.
Examples: "38.5", "42.0", "35.8"

If not found, return "".
""",
            "unit_lifetime": """
From the following content about {plant_name}, extract the expected operational lifetime of this unit in years.
Look for design life, operational life, or expected service duration.

Content: {content}

IMPORTANT: Return ONLY the lifetime number in years, nothing else. No explanations, no additional text.
Examples: "25", "30", "40"

If not found, return "".
""",
            "selected_coal_type": """
From the following content about {plant_name}, determine the type of coal used by this unit.
Look for coal grade information like bituminous, sub-bituminous, lignite, etc.

Content: {content}

IMPORTANT: Return ONLY the coal type, nothing else. No explanations, no additional text.
Valid responses: bituminous, sub-bituminous, lignite, anthracite

If not found, return "".
""",
            "boiler_type": """
From the following content about {plant_name}, determine the boiler technology used by this unit.
Look for boiler type information like pulverized coal, fluidized bed, etc.

Content: {content}

IMPORTANT: Return ONLY the boiler type, nothing else. No explanations, no additional text.
Examples: "pulverized coal", "circulating fluidized bed", "bubbling fluidized bed"

If not found, return "".
"""
        }


# Global configuration instance
config = Config()
