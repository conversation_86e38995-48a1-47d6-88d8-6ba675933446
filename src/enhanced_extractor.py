"""
Enhanced extraction orchestrator with multi-source validation and fallback strategies.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from src.models import ScrapedContent, ExtractionResult, OrganizationalDetails
from src.groq_client import GroqExtractionClient, PowerPlantDataExtractor
from src.openai_client import OpenAIExtractionClient
from src.validation import DataValidator, CrossSourceValidator
from src.html_first_extractor import HtmlFirstExtractor
from src.config import config

logger = logging.getLogger(__name__)


class EnhancedDataExtractor:
    """Enhanced extractor with multi-source validation and fallback strategies."""

    def __init__(self, groq_api_key: str = None, use_bedrock: bool = False, use_openai: bool = False, openai_api_key: str = None, use_groq: bool = True):
        if use_openai and openai_api_key:
            # Use OpenAI as primary LLM
            self.llm_client = OpenAIExtractionClient(
                api_key=openai_api_key,
                model=config.pipeline.openai_model
            )
            logger.info("Enhanced data extractor initialized with OpenAI")
        elif use_groq and groq_api_key:
            # Use Groq
            self.llm_client = GroqExtractionClient(groq_api_key)
            logger.info("Enhanced data extractor initialized with Groq")
        else:
            raise ValueError("No valid LLM client configuration provided")

        # Keep groq_client for backward compatibility
        self.groq_client = self.llm_client
        self.validator = DataValidator()
        self.cross_validator = CrossSourceValidator()

    async def extract_with_validation(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str
    ) -> OrganizationalDetails:
        """
        Extract data with enhanced validation and fallback strategies.

        Args:
            scraped_contents: List of scraped web content
            plant_name: Name of the power plant

        Returns:
            Validated OrganizationalDetails object
        """
        logger.info(f"Starting enhanced extraction for {len(scraped_contents)} content pieces")

        if not scraped_contents:
            logger.warning("No scraped content available")
            return OrganizationalDetails()

        try:
            # Strategy 1: Extract from combined content (original approach)
            combined_extraction = await self._extract_from_combined_content(
                scraped_contents, plant_name
            )

            # Strategy 2: Extract from individual high-quality sources
            individual_extractions = await self._extract_from_individual_sources(
                scraped_contents, plant_name
            )

            # Strategy 3: Extract from source-type grouped content
            grouped_extractions = await self._extract_from_grouped_sources(
                scraped_contents, plant_name
            )

            # Combine all extraction attempts
            all_extractions = [combined_extraction] + individual_extractions + grouped_extractions

            # Cross-validate and get final result
            validated_result = await self.cross_validator.validate_multi_source_extraction(
                all_extractions, plant_name
            )

            logger.info("Enhanced extraction completed successfully")
            return validated_result

        except Exception as e:
            logger.error(f"Enhanced extraction failed: {e}")
            # Fallback to basic extraction
            return await self._fallback_extraction(scraped_contents, plant_name)

    async def _extract_from_combined_content(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str
    ) -> Dict[str, Any]:
        """Extract from combined content (original approach)."""
        logger.info("Strategy 1: Extracting from combined content")

        try:
            extractor = PowerPlantDataExtractor(self.groq_client)
            extracted_data = await extractor.extract_all_fields(scraped_contents, plant_name)

            # Ensure plant_types is a list
            if isinstance(extracted_data.get("plant_types"), str):
                extracted_data["plant_types"] = [extracted_data["plant_types"]]

            # Convert to extraction results format
            extraction_results = []
            for field_name, value in extracted_data.items():
                if value not in [None, "", []]:
                    confidence = 0.7  # Default confidence for combined extraction
                    extraction_results.append(ExtractionResult(
                        field_name=field_name,
                        extracted_value=value,
                        confidence_score=confidence,
                        source_url="combined_content",
                        extraction_method="combined_groq"
                    ))

            return {
                "method": "combined_content",
                "extraction_results": extraction_results,
                "source_count": len(scraped_contents)
            }

        except Exception as e:
            logger.error(f"Combined content extraction failed: {e}")
            return {"method": "combined_content", "extraction_results": [], "source_count": 0}

    async def _extract_from_individual_sources(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str
    ) -> List[Dict[str, Any]]:
        """Extract from individual high-quality sources."""
        logger.info("Strategy 2: Extracting from individual sources")

        top_sources = sorted(
            scraped_contents,
            key=lambda x: (
                config.url_priority_weights.get(x.source_type, 0),
                x.relevance_score
            ),
            reverse=True
        )[:3]  # Top 3 sources

        individual_extractions = []

        for i, content in enumerate(top_sources):
            try:
                logger.info(f"Extracting from source {i+1}: {content.url}")

                key_fields = ["organization_name", "cfpp_type", "country_name", "ppa_flag", "plant_types"]
                extraction_results = []

                for field_name in key_fields:
                    try:
                        result = await self.groq_client.extract_field(
                            field_name, content.content, plant_name
                        )

                        # Ensure plant_types is a list
                        if field_name == "plant_types" and isinstance(result.extracted_value, str):
                            result.extracted_value = [result.extracted_value]

                        if result.confidence_score >= 0.5:  # Lower threshold for individual sources
                            result.source_url = content.url
                            result.extraction_method = f"individual_source_{content.source_type}"
                            extraction_results.append(result)

                        await asyncio.sleep(0.3)  # Rate limiting

                    except Exception as e:
                        logger.error(f"Failed to extract {field_name} from {content.url}: {e}")

                if extraction_results:
                    individual_extractions.append({
                        "method": f"individual_source_{i+1}",
                        "extraction_results": extraction_results,
                        "source_url": content.url,
                        "source_type": content.source_type
                    })

            except Exception as e:
                logger.error(f"Individual source extraction failed for {content.url}: {e}")

        logger.info(f"Individual source extraction completed: {len(individual_extractions)} sources")
        return individual_extractions

    async def _extract_from_grouped_sources(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str
    ) -> List[Dict[str, Any]]:
        """Extract from content grouped by source type."""
        logger.info("Strategy 3: Extracting from grouped sources")

        grouped_content = {}
        for content in scraped_contents:
            source_type = content.source_type
            if source_type not in grouped_content:
                grouped_content[source_type] = []
            grouped_content[source_type].append(content)

        grouped_extractions = []

        for source_type, contents in grouped_content.items():
            if len(contents) < 2:  # Skip single-content groups
                continue

            try:
                logger.info(f"Extracting from {source_type} group ({len(contents)} sources)")

                combined_text = "\n\n".join([
                    f"--- {content.url} ---\n{content.content[:3000]}"
                    for content in contents[:3]  # Limit to top 3 per group
                ])

                field_mapping = {
                    "company_official": ["organization_name", "plants_count", "plant_types"],
                    "government_database": ["cfpp_type", "country_name", "province"],
                    "industry_report": ["cfpp_type", "ppa_flag", "plants_count"],
                    "news_article": ["organization_name", "country_name", "cfpp_type"]
                }

                fields_to_extract = field_mapping.get(source_type, ["organization_name", "cfpp_type"])
                extraction_results = []

                for field_name in fields_to_extract:
                    try:
                        result = await self.groq_client.extract_field(
                            field_name, combined_text, plant_name
                        )

                        # Ensure plant_types is a list
                        if field_name == "plant_types" and isinstance(result.extracted_value, str):
                            result.extracted_value = [result.extracted_value]

                        if result.confidence_score >= 0.6:
                            result.source_url = f"grouped_{source_type}"
                            result.extraction_method = f"grouped_{source_type}"
                            extraction_results.append(result)

                        await asyncio.sleep(0.3)

                    except Exception as e:
                        logger.error(f"Failed to extract {field_name} from {source_type} group: {e}")

                if extraction_results:
                    grouped_extractions.append({
                        "method": f"grouped_{source_type}",
                        "extraction_results": extraction_results,
                        "source_count": len(contents)
                    })

            except Exception as e:
                logger.error(f"Grouped extraction failed for {source_type}: {e}")

        logger.info(f"Grouped source extraction completed: {len(grouped_extractions)} groups")
        return grouped_extractions

    async def _fallback_extraction(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str
    ) -> OrganizationalDetails:
        """Fallback to basic extraction when enhanced methods fail."""
        logger.info("Using fallback extraction method")

        try:
            # Use original PowerPlantDataExtractor as fallback
            extractor = PowerPlantDataExtractor(self.groq_client)
            extracted_data = await extractor.extract_all_fields(scraped_contents, plant_name)

            return OrganizationalDetails(**extracted_data)

        except Exception as e:
            logger.error(f"Fallback extraction failed: {e}")
            return OrganizationalDetails()


class AdaptiveExtractor:
    """Adaptive extractor that adjusts strategy based on content quality and availability."""

    def __init__(self, groq_api_key: str = None, use_bedrock: bool = False, use_openai: bool = False, openai_api_key: str = None, use_groq: bool = True):
        self.enhanced_extractor = EnhancedDataExtractor(
            groq_api_key,
            use_bedrock=use_bedrock,
            use_openai=use_openai,
            openai_api_key=openai_api_key,
            use_groq=use_groq
        )

        if use_openai and openai_api_key:
            # Use OpenAI for basic extractor too
            from src.openai_client import PowerPlantDataExtractor as OpenAIPowerPlantDataExtractor
            openai_client = OpenAIExtractionClient(
                api_key=openai_api_key,
                model=config.pipeline.openai_model
            )
            self.basic_extractor = OpenAIPowerPlantDataExtractor(openai_client)
            self.html_first_extractor = HtmlFirstExtractor(groq_api_key) if groq_api_key else None
        elif use_groq and groq_api_key:
            # Use Groq for all extractors
            from src.groq_client import PowerPlantDataExtractor
            self.basic_extractor = PowerPlantDataExtractor(GroqExtractionClient(groq_api_key))
            self.html_first_extractor = HtmlFirstExtractor(groq_api_key)
        else:
            raise ValueError("No valid LLM client configuration provided")

    async def extract_adaptively(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str
    ) -> OrganizationalDetails:
        """
        Adaptively choose extraction strategy based on content characteristics.

        Args:
            scraped_contents: List of scraped web content
            plant_name: Name of the power plant

        Returns:
            OrganizationalDetails object
        """
        if not scraped_contents:
            return OrganizationalDetails()

        # Analyze content characteristics
        content_analysis = self._analyze_content_quality(scraped_contents)

        logger.info(f"Content analysis: {content_analysis}")

        # Choose strategy based on analysis
        has_pdfs = any(content.url.lower().endswith('.pdf') or '.pdf?' in content.url.lower()
                      for content in scraped_contents)

        if has_pdfs and content_analysis["total_sources"] >= 3:
            # Use HTML-first strategy when PDFs are available
            logger.info("Using HTML-first extraction strategy (PDFs available)")
            return await self.html_first_extractor.extract_with_html_first_strategy(scraped_contents, plant_name)

        elif content_analysis["high_quality_sources"] >= 3 and content_analysis["total_content"] > 10000:
            # Use enhanced multi-source extraction
            logger.info("Using enhanced multi-source extraction strategy")
            return await self.enhanced_extractor.extract_with_validation(scraped_contents, plant_name)

        elif content_analysis["total_sources"] >= 2:
            # Use basic extraction with validation
            logger.info("Using basic extraction with validation")
            extracted_data = await self.basic_extractor.extract_all_fields(scraped_contents, plant_name)
            return OrganizationalDetails(**extracted_data)

        else:
            # Use single-source extraction
            logger.info("Using single-source extraction")
            if scraped_contents:
                best_content = max(scraped_contents, key=lambda x: x.relevance_score)
                extracted_data = await self.basic_extractor.extract_all_fields([best_content], plant_name)
                return OrganizationalDetails(**extracted_data)
            else:
                return OrganizationalDetails()

    def _analyze_content_quality(self, scraped_contents: List[ScrapedContent]) -> Dict[str, Any]:
        """Analyze the quality and characteristics of scraped content."""
        total_content = sum(len(content.content) for content in scraped_contents)
        avg_relevance = sum(content.relevance_score for content in scraped_contents) / len(scraped_contents)

        high_quality_sources = sum(
            1 for content in scraped_contents
            if content.relevance_score > 0.5 and
               config.url_priority_weights.get(content.source_type, 0) >= 7
        )

        source_types = set(content.source_type for content in scraped_contents)

        return {
            "total_sources": len(scraped_contents),
            "total_content": total_content,
            "avg_relevance": avg_relevance,
            "high_quality_sources": high_quality_sources,
            "source_type_diversity": len(source_types),
            "source_types": list(source_types)
        }
