#!/usr/bin/env python3
"""
Optimized Power Plant Data Extraction Pipeline
Focuses on cache-first approach to minimize API calls and avoid rate limits

This pipeline implements a cache-optimized workflow:
1. Level 1: Search plant name → Top 5 links → Scrape → LLM fills org_details → Cache
2. Level 2: Use cached content ONLY for plant_details (no additional searches)
3. Level 3: Use cached content ONLY for unit_details (no additional searches)

Usage: python optimized_pipeline.py "Plant Name"
Example: python optimized_pipeline.py "Jhajjar Power Plant"
"""
import asyncio
import json
import logging
import sys
import time
from datetime import datetime
from typing import Dict, Any, List, Tuple

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OptimizedPowerPlantPipeline:
    """Cache-optimized power plant extraction pipeline with minimal API calls."""

    def __init__(self, plant_name: str):
        """Initialize the pipeline for a specific plant."""
        self.plant_name = plant_name
        self.plant_safe_name = plant_name.lower().replace(" ", "_").replace("-", "_")

        # Initialize cache memory for all levels
        self.cache_memory = {
            'scraped_content': [],
            'org_details': {},
            'plant_details': {},
            'unit_details': []
        }

        # Initialize clients and extractors
        from src.config import config
        from src.enhanced_extractor import AdaptiveExtractor
        from src.plant_details_extractor import PlantDetailsExtractor
        from src.unit_details_extractor import UnitDetailsExtractor

        self.serp_api_key = config.pipeline.serp_api_key
        self.scraper_api_key = config.pipeline.scraper_api_key
        self.groq_api_key = config.pipeline.groq_api_key

        # Initialize extractors
        self.org_extractor = AdaptiveExtractor(
            groq_api_key=self.groq_api_key,
            use_bedrock=False,
            use_openai=False
        )
        # Initialize Groq client for plant extractor
        from src.groq_client import GroqExtractionClient
        groq_client = GroqExtractionClient(self.groq_api_key)
        self.plant_extractor = PlantDetailsExtractor(groq_client)
        self.unit_extractor = UnitDetailsExtractor()

    async def run_cache_optimized_extraction(self) -> Tuple[Dict[str, Any], Dict[str, Any], List[Dict[str, Any]]]:
        """
        Run the cache-optimized three-level extraction pipeline:
        1. Search plant name → Top 5 links → Scrape → LLM fills org_details → Cache
        2. Use cached content ONLY for plant_details (no additional API calls)
        3. Use cached content ONLY for unit_details (no additional API calls)

        Returns:
            Tuple of (org_details, plant_details, unit_details_list)
        """
        print("🚀 CACHE-OPTIMIZED POWER PLANT DATA EXTRACTION PIPELINE")
        print("=" * 70)
        print(f"🔍 Target Plant: {self.plant_name}")
        print(f"🧠 Method: Cache-First Approach (Minimal API Calls)")
        print(f"📊 Workflow: Single Search → Cache → Extract All Levels")
        print("=" * 70)

        start_time = time.time()

        # LEVEL 1: Organizational Details Extraction with Caching
        print("\n📊 LEVEL 1: ORGANIZATIONAL DETAILS EXTRACTION")
        print("-" * 50)
        org_details = await self._level_1_organizational_extraction()
        self._display_level_summary("Organizational", org_details, 1)

        # LEVEL 2: Plant Details Extraction (Cache-Only)
        print("\n🏭 LEVEL 2: PLANT DETAILS EXTRACTION (CACHE-ONLY)")
        print("-" * 50)
        plant_details = await self._level_2_plant_extraction_cache_only()
        self._display_level_summary("Plant Technical", plant_details, 2)

        # LEVEL 3: Unit Details Extraction (Cache-Only)
        print("\n⚡ LEVEL 3: UNIT DETAILS EXTRACTION (CACHE-ONLY)")
        print("-" * 50)
        unit_details_list = await self._level_3_unit_extraction_cache_only()
        self._display_unit_summary(unit_details_list)

        # Save only the final JSON results to workspace
        print("\n💾 SAVING FINAL JSON RESULTS TO WORKSPACE")
        print("-" * 50)
        await self._save_final_json_results(org_details, plant_details, unit_details_list)

        total_time = time.time() - start_time
        total_fields = self._count_total_fields(org_details, plant_details, unit_details_list)

        print(f"\n🎉 CACHE-OPTIMIZED EXTRACTION FINISHED!")
        print(f"⏱️  Total time: {total_time:.1f} seconds")
        print(f"📊 Total data points extracted: {total_fields}")
        print(f"🧠 Strategy: Cache-first approach (minimal API calls)")
        print(f"💾 Cache efficiency: Single search, maximum reuse")

        return org_details, plant_details, unit_details_list

    async def _level_1_organizational_extraction(self) -> Dict[str, Any]:
        """
        LEVEL 1: Search plant name → Top 5 links → Scrape → LLM fills org_details → Cache
        """
        print("🔍 Step 1: Searching Google for plant name...")

        try:
            # Step 1: Search Google for plant name and get top 5 links
            from src.serp_client import SerpAPIClient
            from src.scraper_client import ScraperAPIClient

            print(f"   🔍 Searching Google for: {self.plant_name}")
            async with SerpAPIClient(self.serp_api_key) as serp_client:
                search_results = await serp_client.search(self.plant_name, num_results=5)
                print(f"   ✅ Found {len(search_results)} search results")

            # Step 2: Scrape content from top 5 links using ScraperAPI with rate limiting
            print("   📄 Step 2: Scraping content from top 5 links...")
            scraped_contents = []
            async with ScraperAPIClient(self.scraper_api_key) as scraper_client:
                for i, result in enumerate(search_results[:5]):
                    try:
                        print(f"      📄 Scraping page {i+1}/5: {result.title[:50]}...")
                        await asyncio.sleep(3)  # Conservative rate limiting
                        scraped_content = await scraper_client.scrape_url(result.url)
                        if scraped_content and scraped_content.content and len(scraped_content.content) > 200:
                            scraped_contents.append(scraped_content)
                    except Exception as scrape_error:
                        if "too many requests" in str(scrape_error).lower() or "429" in str(scrape_error):
                            print(f"      ⚠️  Rate limit hit, waiting longer...")
                            await asyncio.sleep(15)  # Long wait for rate limit
                        else:
                            logger.warning(f"Failed to scrape {result.url}: {scrape_error}")

            print(f"   ✅ Successfully scraped {len(scraped_contents)} pages")

            # Step 3: Combine all scraped data and cache
            print("   🔗 Step 3: Combining all scraped data...")
            self.cache_memory['scraped_content'] = scraped_contents
            print(f"   💾 Cached {len(scraped_contents)} pages in memory for reuse")

            # Step 4: LLM fills org_details from combined content
            print("   🧠 Step 4: LLM processing content to fill org_details...")
            org_details = await self.org_extractor.extract_adaptively(scraped_contents, self.plant_name)

            # Convert to dict if it's a Pydantic model
            if hasattr(org_details, 'model_dump'):
                org_details = org_details.model_dump()

            # Step 5: Save org_details to cache memory
            self.cache_memory['org_details'] = org_details
            print("   💾 Saved org_details to cache memory")

            print("✅ Level 1 organizational extraction completed")
            return org_details

        except Exception as e:
            logger.error(f"Level 1 organizational extraction failed: {e}")
            print(f"❌ Level 1 organizational extraction failed: {e}")
            return {}

    async def _level_2_plant_extraction_cache_only(self) -> Dict[str, Any]:
        """
        LEVEL 2: Use cached content ONLY for plant_details (no additional API calls)
        """
        print("🔍 Using cached content for plant technical information...")

        try:
            # Get cached data
            cached_content = self.cache_memory['scraped_content']
            org_details = self.cache_memory['org_details']

            if not cached_content:
                print("   ⚠️  No cached content available, skipping plant extraction")
                return {}

            print(f"   💾 Using {len(cached_content)} cached pages from Level 1")

            # Check if multiple plants need to be processed
            plants_count = org_details.get('plants_count', 1) if org_details else 1
            print(f"   🏭 Processing {plants_count} plant(s) based on org_details")

            # Extract plant details using cached content ONLY
            print("   🧠 LLM processing cached content for plant_details...")
            # Convert org_details dict to object-like access if needed
            if isinstance(org_details, dict):
                class OrgDetailsObj:
                    def __init__(self, data):
                        for key, value in data.items():
                            setattr(self, key, value)
                org_details_obj = OrgDetailsObj(org_details)
            else:
                org_details_obj = org_details

            plant_details = await self.plant_extractor.extract_all_plant_details(
                cached_content, self.plant_name, org_details_obj
            )

            # Convert to dict if it's a Pydantic model
            if hasattr(plant_details, 'model_dump'):
                plant_details = plant_details.model_dump()
            elif plant_details is None:
                plant_details = {}

            # Save plant_details to cache memory
            self.cache_memory['plant_details'] = plant_details
            print("   💾 Saved plant_details to cache memory")

            print("✅ Level 2 plant extraction completed (cache-only)")
            return plant_details

        except Exception as e:
            logger.error(f"Level 2 plant extraction failed: {e}")
            print(f"❌ Level 2 plant extraction failed: {e}")
            return {}

    async def _level_3_unit_extraction_cache_only(self) -> List[Dict[str, Any]]:
        """
        LEVEL 3: Use cached content ONLY for unit_details (no additional API calls)
        """
        print("🔍 Using cached content for unit information...")

        try:
            # Get cached data
            cached_content = self.cache_memory['scraped_content']
            plant_details = self.cache_memory['plant_details']

            # Get number of units from plant_details
            units_id = plant_details.get("units_id", [1, 2])
            if not units_id:
                units_id = [1, 2]  # Default assumption

            print(f"   🔢 Found {len(units_id)} units in plant_details: {units_id}")
            print(f"   💾 Using cached content from Level 1 & 2")

            unit_details_list = []

            for unit_id in units_id:
                print(f"\n   🔧 Processing Unit {unit_id}...")

                # Extract unit details using cached content ONLY
                print(f"      🧠 LLM processing cached content for Unit {unit_id}...")
                unit_details = await self._extract_unit_from_cache_only(unit_id, cached_content)

                unit_details_list.append(unit_details)
                print(f"   ✅ Unit {unit_id} details completed (cache-only)")

            # Save unit_details to cache memory
            self.cache_memory['unit_details'] = unit_details_list
            print("   💾 Saved unit_details to cache memory")

            print("✅ Level 3 unit extraction completed (cache-only)")
            return unit_details_list

        except Exception as e:
            logger.error(f"Level 3 unit extraction failed: {e}")
            print(f"❌ Level 3 unit extraction failed: {e}")
            return []

    async def _extract_unit_from_cache_only(self, unit_id: int, cached_content: List) -> Dict[str, Any]:
        """Extract unit details from cached content only (no additional API calls)."""
        try:
            from src.groq_client import GroqExtractionClient

            # Combine cached content
            combined_content = "\n\n".join([
                content.content if hasattr(content, 'content') else str(content)
                for content in cached_content
            ])

            if not combined_content or len(combined_content) < 100:
                return self._create_empty_unit_structure(unit_id)

            # Extract unit details using LLM based on the exact schema
            groq_client = GroqExtractionClient(self.groq_api_key)

            # Create unit details structure matching the exact JSON schema
            unit_details = {
                "unit_number": unit_id,
                "plant_id": 1,
                "capacity": await self._extract_field_from_content(groq_client, combined_content, "capacity", unit_id),
                "capacity_unit": "MW",
                "fuel_type": await self._extract_field_from_content(groq_client, combined_content, "fuel_type", unit_id),
                "technology": await self._extract_field_from_content(groq_client, combined_content, "technology", unit_id),
                "commencement_date": await self._extract_field_from_content(groq_client, combined_content, "commencement_date", unit_id),
                "heat_rate": await self._extract_field_from_content(groq_client, combined_content, "heat_rate", unit_id),
                "heat_rate_unit": "kJ/kWh",
                "ppa_details": [],
                "gross_power_generation": [],
                "plf": [],
                "PAF": [],
                "emission_factor": [],
                "unit_efficiency": await self._extract_field_from_content(groq_client, combined_content, "unit_efficiency", unit_id),
                "unit_lifetime": await self._extract_field_from_content(groq_client, combined_content, "unit_lifetime", unit_id),
                "remaining_useful_life": "",
                "selected_coal_type": await self._extract_field_from_content(groq_client, combined_content, "selected_coal_type", unit_id),
                "boiler_type": await self._extract_field_from_content(groq_client, combined_content, "boiler_type", unit_id)
            }

            return unit_details

        except Exception as e:
            logger.error(f"Unit extraction from cache failed for Unit {unit_id}: {e}")
            return self._create_empty_unit_structure(unit_id)

    def _create_empty_unit_structure(self, unit_id: int) -> Dict[str, Any]:
        """Create empty unit structure based on the exact unit_details.json schema."""
        return {
            "unit_number": unit_id,
            "plant_id": 1,
            "capacity": "",
            "capacity_unit": "MW",
            "fuel_type": [],
            "technology": "",
            "commencement_date": "",
            "heat_rate": "",
            "heat_rate_unit": "kJ/kWh",
            "ppa_details": [],
            "gross_power_generation": [],
            "plf": [],
            "PAF": [],
            "emission_factor": [],
            "unit_efficiency": "",
            "unit_lifetime": "",
            "remaining_useful_life": "",
            "selected_coal_type": "",
            "boiler_type": ""
        }

    async def _extract_field_from_content(self, groq_client, content: str, field: str, unit_id: int) -> Any:
        """Extract specific field from content using LLM."""
        try:
            # Use the correct extract_field method from GroqExtractionClient
            result = await groq_client.extract_field(field, content, f"{self.plant_name} Unit {unit_id}")
            if result and hasattr(result, 'extracted_value'):
                return result.extracted_value or ""
            return ""

        except Exception as e:
            logger.error(f"Field extraction failed for {field}: {e}")
            return ""

    def _display_level_summary(self, level_name: str, details: Dict[str, Any], level_num: int):
        """Display summary for a specific level."""
        if not details:
            print(f"❌ {level_name} extraction failed - no data retrieved")
            return

        filled_fields = sum(1 for v in details.values() if v not in [None, "", []])
        total_fields = len(details)
        print(f"📊 {level_name} Level {level_num}: {filled_fields}/{total_fields} fields extracted")

        # Show key fields based on level
        if level_num == 1:  # Organizational
            key_fields = ['organization_name', 'country_name', 'province', 'plant_types', 'cfpp_type']
        elif level_num == 2:  # Plant
            key_fields = ['name', 'plant_type', 'lat', 'long', 'units_id']
        else:
            key_fields = list(details.keys())[:5]  # First 5 fields

        for field in key_fields:
            value = details.get(field)
            if value and value not in [None, "", []]:
                if isinstance(value, list):
                    print(f"   • {field}: {', '.join(map(str, value))}")
                else:
                    display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                    print(f"   • {field}: {display_value}")

    def _display_unit_summary(self, unit_details_list: List[Dict[str, Any]]):
        """Display unit details summary."""
        if not unit_details_list:
            print("❌ Unit extraction failed - no units retrieved")
            return

        print(f"⚡ Unit Level 3: {len(unit_details_list)} units extracted")

        for unit in unit_details_list:
            unit_id = unit.get('unit_number', 'Unknown')
            capacity = unit.get('capacity', '')
            technology = unit.get('technology', '')

            print(f"\n   📋 Unit {unit_id}:")
            if capacity:
                print(f"      • Capacity: {capacity} MW")
            if technology:
                print(f"      • Technology: {technology}")

    async def _save_final_json_results(self, org_details: Dict[str, Any], plant_details: Dict[str, Any], unit_details_list: List[Dict[str, Any]]):
        """Save only the final JSON results to workspace as requested."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        try:
            # Save only the three required JSON files to workspace
            org_file = f"{self.plant_safe_name}_org_details_{timestamp}.json"
            with open(org_file, 'w', encoding='utf-8') as f:
                json.dump(org_details, f, indent=2, ensure_ascii=False)
            print(f"📊 org_details.json saved: {org_file}")

            plant_file = f"{self.plant_safe_name}_plant_details_{timestamp}.json"
            with open(plant_file, 'w', encoding='utf-8') as f:
                json.dump(plant_details, f, indent=2, ensure_ascii=False)
            print(f"🏭 plant_details.json saved: {plant_file}")

            units_file = f"{self.plant_safe_name}_unit_details_{timestamp}.json"
            with open(units_file, 'w', encoding='utf-8') as f:
                json.dump(unit_details_list, f, indent=2, ensure_ascii=False)
            print(f"⚡ unit_details.json saved: {units_file}")

            print(f"✅ All three level JSONs saved to workspace")

        except Exception as e:
            logger.error(f"Failed to save JSON results: {e}")
            print(f"❌ Failed to save JSON results: {e}")

    def _count_total_fields(self, org_details: Dict[str, Any], plant_details: Dict[str, Any], unit_details_list: List[Dict[str, Any]]) -> int:
        """Count total fields across all levels."""
        org_count = sum(1 for v in org_details.values() if v not in [None, "", []])
        plant_count = sum(1 for v in plant_details.values() if v not in [None, "", []])

        unit_count = 0
        for unit in unit_details_list:
            unit_count += sum(1 for v in unit.values() if v not in [None, "", []])

        return org_count + plant_count + unit_count


async def main():
    """Main execution function."""
    if len(sys.argv) < 2:
        print("Usage: python optimized_pipeline.py \"Plant Name\"")
        print("Example: python optimized_pipeline.py \"Jhajjar Power Plant\"")
        print("Example: python optimized_pipeline.py \"Vogtle Nuclear Plant\"")
        sys.exit(1)

    plant_name = sys.argv[1]

    print("🚀 CACHE-OPTIMIZED POWER PLANT DATA EXTRACTION PIPELINE")
    print("Minimal API calls with maximum cache reuse")
    print()

    try:
        # Check API keys
        from src.config import config
        if not config.pipeline.serp_api_key:
            print("❌ SCRAPER_API_KEY not found in environment variables")
            sys.exit(1)

        if not config.pipeline.groq_api_key:
            print("❌ GROQ_API_KEY not found in environment variables")
            sys.exit(1)

        # Initialize and run cache-optimized pipeline
        pipeline = OptimizedPowerPlantPipeline(plant_name)
        org_details, plant_details, unit_details_list = await pipeline.run_cache_optimized_extraction()

        # Display final results summary
        print(f"\n📄 CACHE-OPTIMIZED EXTRACTION COMPLETED FOR: {plant_name}")
        print("=" * 70)
        print(f"📊 Organizational fields: {sum(1 for v in org_details.values() if v not in [None, '', []])}")
        print(f"🏭 Plant technical fields: {sum(1 for v in plant_details.values() if v not in [None, '', []])}")
        print(f"⚡ Unit details: {len(unit_details_list)} units with comprehensive specifications")
        print(f"🧠 Strategy: Cache-first approach (minimal API calls)")
        print(f"💾 Efficiency: Single search → Maximum cache reuse → No additional API calls")
        print(f"✅ Complete workflow: Search → Cache → Extract All Levels")

    except Exception as e:
        print(f"\n❌ CACHE-OPTIMIZED EXTRACTION PIPELINE FAILED: {e}")
        logger.error(f"Pipeline failed: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
